import 'dart:async';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../dataModel/data/location.dart';
import '../../../dataModel/data/location_notes.dart';
import '../../../dataModel/data/user.dart';
import '../../../dataModel/data_model.dart';
import '../../../helpers/color_helper.dart';
import '../../../helpers/text_style_helper.dart';
import '../../../misc/app_localization.dart';
import '../../../misc/extensions.dart';
import '../../view_model_mixin.dart';
import '../../../state/permissions_state.dart';
import '../../../widgets/active_toggle.dart';
import '../../../widgets/search_text_field.dart';
import '../../../widgets/tables_global.dart';
import '../employees/employees.dart';
import '../my_scaffold.dart';
import 'location_mixin.dart';

class LocationsPage extends StatelessWidget {
  const LocationsPage({super.key});

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider<_ViewModel>(
        create: (context) => _ViewModel(),
        child: MyScaffold(
          title: AppLocalization.of(context).locations,
          body: _Body(),
        ),
      );
}

class _Body extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Align(
        alignment: Alignment.centerLeft,
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 1300),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              

              _TableHeader(), // Top filter inputs
              const SizedBox(height: 20),
              Flexible(
                child: _LocationTable(), // Custom table widget
              ),
            ],
          ),
        ),
      );
}

class _TableHeader extends StatefulWidget {
  @override
  _TableHeaderState createState() => _TableHeaderState();
}

class _TableHeaderState extends State<_TableHeader> {
  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 600;

    return Consumer<_ViewModel>(
      builder: (context, viewModel, child) => Card(
        elevation: 0,
        color: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: isMobile
              ? Column(
                  children: [
                    SizedBox(
                      width: screenWidth * 0.9,
                      child:
                          SearchTextField(notifier: viewModel.searchNotifier),
                    ),
                    const SizedBox(height: 10),
                    Center(
                      child: ActiveToggle(viewModel.activeNotifier),
                    ),
                    const SizedBox(height: 10),
                    if (PermissionsState().editLocations)
                      ElevatedButton.icon(
                        onPressed: () =>
                            context.go('/locations/edit?anything=1'),
                        icon: const Icon(Icons.add),
                        label: Text(AppLocalization.of(context).addLocation),
                      ),
                  ],
                )
              : Row(
                  children: [
                    Flexible(
                      child: SizedBox(
                        width: 300,
                        child:
                            SearchTextField(notifier: viewModel.searchNotifier),
                      ),
                    ),
                    const SizedBox(width: 20),
                    Padding(
                      padding: const EdgeInsets.all(8),
                      child: ActiveToggle(viewModel.activeNotifier),
                    ),
                    const SizedBox(width: 20),
                    if (PermissionsState().editLocations)
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerRight,
                        child: 
                      Padding(
                        padding: const EdgeInsets.all(8),
                        child: ElevatedButton.icon(
                          style: ButtonStyle(backgroundColor: MaterialStateProperty.all<Color>(ColorHelper.thePunchAdminButtonBlue())  ),
                          onPressed: () =>
                              context.go('/locations/edit?anything=1'),
                          icon: const Icon(Icons.add),
                          label: Text(AppLocalization.of(context).addLocation),
                        ),
                      ),
                     )
                      )
                  ],
                ),
        ),
      ),
    );
  }
}

class _LocationTable extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Consumer<_ViewModel>(
        builder: (context, viewModel, child) {
          final rows = _getRows(viewModel, context);
          return CustomTable(
            columns: _getColumns(context),
            rows: rows,
            onRowClick: (DataRow row) {
              final locationName = (row.cells[0].child as Text).data;
              final selectedLocation = viewModel.locations
                  .firstWhere((location) => location.name == locationName);

              context.pushNamed(
                '/locations/view',
                queryParameters: {'id': selectedLocation.id},
              );
            },
            mobileTableTitle: "Locations",
            headerHeight: 60,
            rowHeight: 40,
            originalData: sele,
            stickyHeader: true,
            headerFontSize: 14, // Custom header font size
            cellFontSize: 13,
            columnWidths: const {
              0: 0.2,
              1: 0.2,
              2: 0.2,
              3: 0.1,
              4: 0.1,
              5: 0.2,

            },
          );
        },
      );

  List<DataColumn> _getColumns(BuildContext context) => [
        DataColumn(label: Text(AppLocalization.of(context).locationName)),
        DataColumn(label: Text(AppLocalization.of(context).contacts)),
        DataColumn(label: Text(AppLocalization.of(context).address)),
        DataColumn(label: Text(AppLocalization.of(context).phone)),
        DataColumn(label: Expanded(child: Center(child: Text(AppLocalization.of(context).active)))),
         DataColumn(label: Expanded(child: Center(child: const Text('View')))),
      ];

  List<DataRow> _getRows(_ViewModel viewModel, BuildContext context) {
    if (!viewModel.isRefreshed) return [];

    Iterable<Location> locations = viewModel.locations;

    switch (viewModel.activeNotifier.value) {
      case ActiveToggleState.active:
        locations = locations.where((e) => e.isActive);
        break;
      case ActiveToggleState.inactive:
        locations = locations.where((e) => !e.isActive);
        break;
      default:
        break;
    }

    final search = viewModel.searchNotifier.value;
    if (search.isNotEmpty) {
      locations = locations.where((e) =>
          e.name.toLowerCase().contains(search.toLowerCase()) ||
          e.address1.toLowerCase().contains(search.toLowerCase()) ||
          e.phone.contains(search) ||
          e.emailAddress.toLowerCase().contains(search));
    }

    final visibleLocations = locations.toList();

    return visibleLocations.map((location) {
      var address = location.address1;
      if (location.address2.isNotEmpty) {
        address += '\n${location.address2}';
      }

      return DataRow(
        onSelectChanged: (value) async => context.pushNamed(
          '/locations/view',
          queryParameters: {'id': location.id},
        ),
        cells: [
          DataCell(Text(location.name, style: const TextStyle(fontWeight: FontWeight.bold))),
            DataCell(viewModel.contactsForLocation(location.id).isEmpty 
              ? const Text('')
              : Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                ),
                child: Text(viewModel.contactsForLocation(location.id).ellipsis(10)),
              )),
            DataCell(Text(address.ellipsis(22))),
            DataCell(TextButton(
            onPressed: () => launchUrl(Uri.parse('tel:+1${location.phone}')),
            child: Text( formatPhoneNumber(location.phone), style: const TextStyle(color: Colors.blue)),
            )),
            // DataCell(Center(child: location.isActive 
            //   ? Image.asset('images/custom_location.png', width: 24, height: 24)
            //   : Icon(Symbols.where_to_vote, color: Colors.grey))),
                          DataCell(Center(child: location.isActive 
              ? Image.asset('images/custom_location.png', width: 24, height: 24): Icon(Symbols.where_to_vote, color: Colors.grey))),
          DataCell(

                        Center(
                        child: ElevatedButton(
                          onPressed: () async => context.pushNamed(
                          '/locations/view',
                          queryParameters: {'id': location.id},
                          ),
                          child: const Text('View'),
                          style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8.0, vertical: 4.0),
                          textStyle: const TextStyle(fontSize: 12),
                          foregroundColor: Colors.blue,
                          backgroundColor: Colors.white,
                          side: const BorderSide(color: Colors.blue),
                          ),
                        ),
                        )
          ),
        ],
      );
    }).toList();
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin, LocationMixin {
  var locations = <Location>[];
  var locationContactsMap = <String, Iterable<User>>{};
  var locationNotesMap = <String, List<LocationNote>>{};
  var activeNotifier = ValueNotifier(ActiveToggleState.active);
  var searchNotifier = ValueNotifier('');
  var locationsNotifier = ValueNotifier<List<Location>>(<Location>[]);

  _ViewModel() {
    addListenables([
      DataModel().userModel,
      DataModel().locationModel,
      DataModel().locationContactModel,
    ]);
    unawaited(refresh());
    searchNotifier.addListener(notifyListeners);
    activeNotifier.addListener(notifyListeners);
  }

  @override
  Future<void> refresh() async {
    locations = (await DataModel().locationModel.all).toList();
    locationsNotifier.value = locations;
    final locationIds = locations.map((e) => e.id);

    final locationContacts =
        await DataModel().locationContactModel.getByLocationIds(locationIds);
    final userIds = locationContacts.map((e) => e.userId).toSet();
    final contacts = (await DataModel().userModel.getByIds(userIds)).toList();
    locationContactsMap = {
      for (final locationId in locationIds)
        locationId: locationContacts
            .where(
                (locationContact) => locationContact.locationId == locationId)
            .map((locationContact) =>
                contacts.firstWhere((e) => e.id == locationContact.userId))
    };

    final locationNotes =
        await DataModel().locationNoteModel.getByLocationIds(locationIds);
    locationNotesMap = {
      for (final locationId in locationIds)
        locationId: locationNotes
            .where((note) => note.locationId == locationId)
            .toList()
    };

    notifyListeners();
  }

  String contactsForLocation(String id) {
    final contacts = locationContactsMap[id]?.map((e) => e.name).toList();
    if (contacts == null || contacts.isEmpty) return '';
    if (contacts.length == 1) return contacts.first;
    return '${contacts.first}.. +${contacts.length - 1}';
  }
}
