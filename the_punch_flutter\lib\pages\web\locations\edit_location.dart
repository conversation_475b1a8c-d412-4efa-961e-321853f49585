import 'dart:async';
import 'dart:ui' as ui; // needed for custom marker painting

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:uuid/uuid.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart' as geo;

import '../../../api/api_model.dart';
import '../../../api/sync_model.dart';
import '../../../dataModel/data/location.dart' as my_app; // your Location model
import '../../../dataModel/data/location_notes.dart';
import '../../../dataModel/data_model.dart';
import '../../../dialogs/time_zone_dialog.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../../helpers/color_helper.dart';
import '../../../misc/extensions.dart';
import '../../../misc/window_time_zone.dart';
import '../../../state/login_state.dart';
import '../../../state/permissions_state.dart';
import '../../../state/server_time_state.dart';
import '../../../widgets/active_toggle.dart';
import '../../../widgets/decorated_text_field.dart';
import '../my_scaffold.dart';

/// Custom ScrollBehavior to hide default scroll glow on desktop/web
class CustomScrollBehavior extends ScrollBehavior {
  @override
  Widget buildViewportChrome(
    BuildContext context,
    Widget child,
    AxisDirection axisDirection,
  ) => child;
}

/// Top-level widget for editing/adding a location.
class EditLocationPage extends StatelessWidget {
  final String locationId;
  final String anything; // just an extra param you mentioned

  // NOTE: removed 'const' because this constructor depends on runtime data.
  EditLocationPage(
    Map<String, String> queryParms, {
    Key? key,
  })  : locationId = queryParms['id'] ?? '',
        anything = queryParms['anything'] ?? '',
        super(key: key);

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider<EditLocationViewModel>(
      create: (_) => EditLocationViewModel(locationId),
      builder: (_, __) => MyScaffold(
        title: anything == '1'
            ? AppLocalizations.of(context)!.addLocation
            : AppLocalizations.of(context)!.editLocation,
        body: const _EditLocationBody(),
      ),
    );
}

/// Main body for editing a location.
class _EditLocationBody extends StatelessWidget {
  const _EditLocationBody();

  @override
  Widget build(BuildContext context) {
    final vm = context.watch<EditLocationViewModel>();

    return Align(
      alignment: Alignment.centerLeft,
      child: Padding(
        padding:  EdgeInsets.zero,
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 1300),
          child: ScrollConfiguration(
            behavior: CustomScrollBehavior(),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _BodyHeader(),
                  const SizedBox(height: 25),
                  _Section(title: 'Location Details:', child: _Form1()),
                  const SizedBox(height: 25),
                  _Section(title: 'Address:', child: _Form2()),
                  const SizedBox(height: 25),
                  _Section(title: 'Location Notes:', child: _FormNotes()),
                  const SizedBox(height: 25),
                  _Section(title: 'Time Zone:', child: _Form3()),
                  const SizedBox(height: 25),
                  _Section(title: 'Geo Fence Size:', child: _Form4()),
                  const SizedBox(height: 25),
                  const Padding(
                    padding: EdgeInsets.all(0),
                    child: SizedBox(
                      height: 400,
                      child: _MapSection(),
                    ),
                  ),
                  // Save button at the bottom:
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(
                              vertical: 20, horizontal: 40),
                            backgroundColor: ColorHelper.thePunchRed(),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(15),
                            ),
                          ),
                          onPressed: vm.canSave && !vm.isLoading
                              ? () async {
                                  await vm.saveAndSync();
                                  Navigator.of(context).pop();
                                }
                              : null,
                          child: vm.isLoading
                              ? const CircularProgressIndicator(
                                  color: Colors.white,
                                )
                              : Text(
                                  AppLocalizations.of(context)!.save,
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 25),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// A styled container that forms a “section” with a title and child widget.
class _Section extends StatelessWidget {
  final String title;
  final Widget child;

  const _Section({
    required this.title,
    required this.child,
  });

  // Helper method to get appropriate icon for each section
  IconData _getIconForSection(String title) {
    switch (title) {
      case 'Location Details:':
        return Icons.business_outlined;
      case 'Address:':
        return Icons.location_on_outlined;
      case 'Location Notes:':
        return Icons.note_outlined;
      case 'Time Zone:':
        return Icons.access_time_outlined;
      case 'Geo Fence Size:':
        return Icons.adjust_outlined;
      default:
        return Icons.info_outline;
    }
  }

  @override
  Widget build(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 600;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title with icon
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                _getIconForSection(title),
                size: 20,
                color: Colors.black87,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w700,
                      fontSize: 18,
                      color: Colors.black87,
                    ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        // Section content
        Container(
          decoration: BoxDecoration(
            color: Colors.grey[50], // Light gray background
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.all(16),
          child: isMobile
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [child],
                )
              : child,
        ),
      ],
    );
  }
}

/// The top region with Cancel button, location name, and an Active switch
class _BodyHeader extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final vm = context.watch<EditLocationViewModel>();
    final location = vm.location;
    if (location == null) {
      return const SizedBox.shrink();
    }

    final title = location.name.isEmpty
        ? ''
        : location.name;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context)
                .textTheme
                .titleLarge
                ?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Cancel button:
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 30),
                    backgroundColor: ColorHelper.thePunchAdminButtonBlue(),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(15),
                    ),
                  ),
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(
                    AppLocalizations.of(context)!.cancel,
                    style: Theme.of(context).textTheme.labelLarge?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),

                // Toggle Active if allowed and not the primary location:
                if (PermissionsState().editLocations &&
                    location.id != my_app.Location.primaryLocationId)
                  ActiveSwitch(
                    value: location.isActive,
                    onChanged: vm.setActive,
                    enableDialog: false,
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// The “Location Details” form (name, phone, email).
class _Form1 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final vm = context.watch<EditLocationViewModel>();
    final loc = vm.location;
    if (loc == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        DecoratedTextField(
          autofocus: true,
          padding: const EdgeInsets.all(8),
          initialValue: loc.name,
          labelText: AppLocalizations.of(context)!.locationName,
          onChanged: vm.setName,
          validator: (value) =>
              (value == null || value.length > 5 || value.isEmpty)
                  ? null
                  : 'Invalid location name.',
        ),
        const SizedBox(height: 16),
        IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Flexible(
                flex: 15,
                child: DecoratedTextField(
                  padding: const EdgeInsets.all(8),
                  initialValue: loc.phone,
                  labelText: AppLocalizations.of(context)!.phone,
                  onChanged: vm.setPhone,
                  validator: (value) =>
                      (value == null || value.length > 7 || value.isEmpty)
                          ? null
                          : 'Invalid Phone Number.',
                ),
              ),
              const SizedBox(width: 16),
              Flexible(
                flex: 20,
                child: DecoratedTextField(
                  padding: const EdgeInsets.all(8),
                  initialValue: loc.emailAddress,
                  labelText: AppLocalizations.of(context)!.emailAddress,
                  onChanged: vm.setEmail,
                  validator: (value) =>
                      (value == null ||
                              value.length > 10 ||
                              value.isEmpty ||
                              value.validateEmail)
                          ? null
                          : 'Invalid email address.',
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

/// The “Address” form (address1, address2, city, state, zip).
class _Form2 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final vm = context.watch<EditLocationViewModel>();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: IntrinsicHeight(
            child: Flex(
              direction: MediaQuery.of(context).size.width < 600
                  ? Axis.vertical
                  : Axis.horizontal,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Flexible(
                  flex: 25,
                  child: DecoratedTextField(
                    controller: vm.address1Controller,
                    padding: const EdgeInsets.all(8),
                    labelText: AppLocalizations.of(context)!.address1,
                    onChanged: vm.setAddress1,
                    validator: (value) =>
                        (value == null || value.length > 10 || value.isEmpty)
                            ? null
                            : 'Invalid address line 1.',
                  ),
                ),
                const SizedBox(width: 8),
                Flexible(
                  flex: 10,
                  child: DecoratedTextField(
                    controller: vm.address2Controller,
                    padding: const EdgeInsets.all(8),
                    labelText: AppLocalizations.of(context)!.address2,
                    onChanged: vm.setAddress2,
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 8),
        IntrinsicHeight(
          child: Flex(
            direction: MediaQuery.of(context).size.width < 600
                ? Axis.vertical
                : Axis.horizontal,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Flexible(
                flex: 15,
                child: DecoratedTextField(
                  controller: vm.cityController,
                  padding: const EdgeInsets.all(8),
                  labelText: AppLocalizations.of(context)!.city,
                  onChanged: vm.setCity,
                  validator: (value) =>
                      (value == null || value.length > 3 || value.isEmpty)
                          ? null
                          : 'Invalid City.',
                ),
              ),
              const SizedBox(width: 8),
              Flexible(
                flex: 20,
                child: DecoratedTextField(
                  controller: vm.stateController,
                  padding: const EdgeInsets.all(8),
                  labelText: AppLocalizations.of(context)!.state,
                  onChanged: vm.setState,
                  validator: (value) =>
                      (value == null || value.length < 20 || value.isEmpty)
                          ? null
                          : 'Invalid State',
                ),
              ),
              const SizedBox(width: 8),
              Flexible(
                flex: 20,
                child: DecoratedTextField(
                  controller: vm.zipController,
                  padding: const EdgeInsets.all(8),
                  labelText: AppLocalizations.of(context)!.zip,
                  onChanged: vm.setZip,
                  validator: (value) =>
                      (value == null || value.length > 4 || value.isEmpty)
                          ? null
                          : 'Invalid Zip Code.',
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

/// The “Location Notes” form section, which opens a BulletPointsDialog.
class _FormNotes extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final vm = context.watch<EditLocationViewModel>();

    Future<void> showNotesDialog() async {
      await showDialog(
        context: context,
        builder: (_) => BulletPointsDialog(
          initialNotes: vm.locationNotes.map((n) => n.toJson()).toList(),
          onSave: (updatedNotes) {
            final newNotes = updatedNotes.map(LocationNote.fromJson).toList();
            vm.setLocationNotes(newNotes);
            vm.updateNotesText();
          },
          locationId: vm.locationId,
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.15),
            spreadRadius: 2,
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: InkWell(
        onTap: showNotesDialog,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                vm.notesController.text.isEmpty
                    ? 'Set Location Notes'
                    : vm.notesController.text,
                style: const TextStyle(
                  color: Colors.black87,
                  fontSize: 16,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const Icon(Icons.edit_outlined, color: Colors.black54),
          ],
        ),
      ),
    );
  }
}

/// The “Time Zone” form section (opens a TimeZoneDialog).
class _Form3 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final vm = context.watch<EditLocationViewModel>();
    final loc = vm.location;
    if (loc == null) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.15),
              spreadRadius: 2,
              blurRadius: 8,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: InkWell(
          onTap: () async {
            // Show time zone dialog:
            await showDialog(
              context: context,
              builder: (_) => TimeZoneDialog(
                selected: vm.setTimeZone,
              ),
            );
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                WindowsTimeZone.ianaToName(loc.timeZone) ?? 'Set Time Zone',
                style: const TextStyle(
                  color: Colors.black87,
                  fontSize: 16,
                ),
              ),
              const Icon(Icons.arrow_drop_down_outlined, color: Colors.black54),
            ],
          ),
        ),
      ),
    );
  }
}

/// The “Geo Fence” text field (read-only).
class _Form4 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final vm = context.watch<EditLocationViewModel>();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.15),
            spreadRadius: 2,
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: TextField(
        readOnly: true,
        controller: vm.geoFenceController,
        decoration: const InputDecoration(
          labelText: 'GeoFence Radius (meters)',
          border: OutlineInputBorder(),
          isDense: true,
        ),
      ),
    );
  }
}

/// A map widget with a draggable circle handle to adjust the geofence radius.
class _MapSection extends StatefulWidget {
  const _MapSection({Key? key}) : super(key: key);

  @override
  State<_MapSection> createState() => _MapSectionState();
}

/// Returns a custom [BitmapDescriptor] that draws an icon + text in a small pop-up shape.
Future<BitmapDescriptor> _createCustomMarkerBitmap(
  String text, {
  double fontSize = 14,
  double iconSize = 18,
  Color textColor = Colors.white,
  Color backgroundColor = Colors.blueAccent,
}) async {
  const double padding = 8;
  const double arrowHeight = 10;

  // 1) Prepare a TextPainter for the icon:
  final iconData = Icons.place_outlined;
  final iconPainter = TextPainter(textDirection: TextDirection.ltr);
  iconPainter.text = TextSpan(
    text: String.fromCharCode(iconData.codePoint),
    style: TextStyle(
      fontFamily: iconData.fontFamily,
      package: iconData.fontPackage,
      fontSize: iconSize,
      color: textColor,
    ),
  );
  iconPainter.layout();

  // 2) Prepare a TextPainter for the label text:
  final textPainter = TextPainter(textDirection: TextDirection.ltr);
  textPainter.text = TextSpan(
    text: text,
    style: TextStyle(
      fontSize: fontSize,
      color: textColor,
      fontWeight: FontWeight.bold,
    ),
  );
  textPainter.layout();

  // 3) Compute total bubble size.
  final double innerWidth = iconPainter.width + 4 + textPainter.width;
  final double width = innerWidth + 2 * padding;
  final double height = (iconPainter.height > textPainter.height
          ? iconPainter.height
          : textPainter.height) +
      2 * padding +
      arrowHeight;

  // 4) Begin drawing on a canvas:
  final pictureRecorder = ui.PictureRecorder();
  final canvas = Canvas(pictureRecorder);

  // 5) Draw the rounded rect “bubble”:
  final bgRect = RRect.fromRectAndRadius(
    Rect.fromLTWH(0, 0, width, height - arrowHeight),
    const Radius.circular(20),
  );
  final bgPaint = Paint()..color = backgroundColor;
  canvas.drawRRect(bgRect, bgPaint);

  // 6) Paint icon and text inside the bubble:
  final iconOffset = Offset(
    padding,
    (height - arrowHeight) / 2 - iconPainter.height / 2,
  );
  iconPainter.paint(canvas, iconOffset);

  final textOffset = Offset(
    padding + iconPainter.width + 4,
    (height - arrowHeight) / 2 - textPainter.height / 2,
  );
  textPainter.paint(canvas, textOffset);

  // 7) Draw the downward arrow at bottom center:
  final arrowPath = Path();
  arrowPath.moveTo(width / 2 - 10, height - arrowHeight);
  arrowPath.lineTo(width / 2 + 10, height - arrowHeight);
  arrowPath.lineTo(width / 2, height);
  arrowPath.close();
  canvas.drawPath(arrowPath, bgPaint);

  // 8) Convert to image, then to raw bytes:
  final picture = pictureRecorder.endRecording();
  final image = await picture.toImage(width.toInt(), height.toInt());
  final bytes = await image.toByteData(format: ui.ImageByteFormat.png);

  // 9) Create a [BitmapDescriptor] from the bytes:
  return BitmapDescriptor.fromBytes(bytes!.buffer.asUint8List());
}

class _MapSectionState extends State<_MapSection> {
  bool _markerCreated = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_markerCreated) {
      _markerCreated = true;
      _loadCustomMarkerIfNeeded();
    }
  }

  Future<void> _loadCustomMarkerIfNeeded() async {
    final vm = context.read<EditLocationViewModel>();
    final loc = vm.location;
    if (loc == null) return;

    final initialRadius = loc.geoFenceRadius <= 0 ? 100 : loc.geoFenceRadius;
    final icon = await _createCustomMarkerBitmap(
      "Adjust Geo Fence (${initialRadius.toStringAsFixed(0)}m)",
      fontSize: 12,
      textColor: Colors.white,
      backgroundColor: Colors.blueAccent,
    );
    if (!mounted) return;
    vm.setRadiusHandleIcon(icon);
  }

  @override
  Widget build(BuildContext context) => Consumer<EditLocationViewModel>(
      builder: (_, vm, __) {
        final loc = vm.location;
        if (loc == null) {
          return const Center(child: CircularProgressIndicator());
        }
        if (vm.mapCenter == null) {
          // If there's no lat/long at all, show a placeholder
          return const Center(
            child: Text('No Lat/Long found. Drag the marker once set.'),
          );
        }

        final circleColor = vm.circleSelected
            ? Colors.blue.withOpacity(0.3)
            : Colors.black.withOpacity(0.3);
        final strokeColor = vm.circleSelected ? Colors.blue : Colors.black;

        final markers = <Marker>[
          // Draggable center marker
          Marker(
            markerId: const MarkerId('center_marker'),
            position: vm.mapCenter!,
            icon: BitmapDescriptor.defaultMarkerWithHue(
              BitmapDescriptor.hueRed,
            ),
            infoWindow: const InfoWindow(title: 'Center'),
            draggable: true,
            onDragEnd: vm.onCenterMarkerDragEnd,
          ),
        ];

        // Draggable radius handle marker
        if (vm.radiusHandle != null) {
          markers.add(
            Marker(
              markerId: const MarkerId('radius_handle'),
              position: vm.radiusHandle!,
              icon: vm.radiusHandleIcon ??
                  BitmapDescriptor.defaultMarkerWithHue(
                    BitmapDescriptor.hueBlue,
                  ),
              draggable: true,
              onDragStart: (_) => vm.setCircleSelected(true),
              onDrag: vm.onRadiusMarkerDrag,
              onDragEnd: (end) {
                vm.onRadiusMarkerDrag(end);
                vm.setCircleSelected(false);
              },
            ),
          );
        }

        return GoogleMap(
          onMapCreated: vm.setMapController,
          initialCameraPosition: CameraPosition(
            target: vm.mapCenter!,
            zoom: 17,
          ),
          mapType: MapType.satellite,
          markers: markers.toSet(),
          circles: {
            Circle(
              circleId: const CircleId('geofence_circle'),
              center: vm.mapCenter!,
              radius: loc.geoFenceRadius,
              fillColor: circleColor,
              strokeColor: strokeColor,
              strokeWidth: 2,
            ),
          },
        );
      },
    );
}

/// Dialog that edits multiple bullet-point notes for a Location.
class BulletPointsDialog extends StatefulWidget {
  final List<Map<String, dynamic>> initialNotes;
  final ValueChanged<List<Map<String, dynamic>>> onSave;
  final String locationId;

  const BulletPointsDialog({
    required this.initialNotes,
    required this.onSave,
    required this.locationId,
    Key? key,
  }) : super(key: key);

  @override
  State<BulletPointsDialog> createState() => _BulletPointsDialogState();
}

class _BulletPointsDialogState extends State<BulletPointsDialog> {
  late List<Map<String, dynamic>> notes;
  var uuid = const Uuid();

  @override
  void initState() {
    super.initState();
    // Filter only active notes:
    notes = widget.initialNotes.where((n) => n['IsActive'] == true).toList();
  }

  void addNote() {
    setState(() {
      notes.add({
        'id': uuid.v4(),
        'LocationId': widget.locationId,
        'Note': '',
        'IsActive': true,
        'CreatedOn': ServerTimeState().utcTime.toString(),
        'CreatedByUserId': LoginState.userId,
        'LastChangedOn': ServerTimeState().utcTime.toString(),
        'LastChangedByUserId': LoginState.userId,
      });
    });
  }

  void updateNoteContent(int index, String value) {
    setState(() {
      notes[index]['Note'] = value;
    });
  }

  void removeNoteContent(int index) {
    setState(() {
      notes.removeAt(index);
    });
    // Immediately reflect changes:
    widget.onSave(notes);
  }

  @override
  Widget build(BuildContext context) => AlertDialog(
      title: const Text('Location Notes:'),
      contentPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
      content: SizedBox(
        width: 750,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              for (int i = 0; i < notes.length; i++)
                ListTile(
                  leading: const Text('•'),
                  title: TextFormField(
                    decoration: const InputDecoration(
                      labelText: '',
                      labelStyle: TextStyle(color: Colors.black),
                      enabledBorder: UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.black),
                      ),
                      focusedBorder: UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.black),
                      ),
                    ),
                    style: const TextStyle(color: Colors.black),
                    initialValue: notes[i]['Note'] ?? '',
                    onChanged: (val) => updateNoteContent(i, val),
                  ),
                  trailing: IconButton(
                    icon: const Icon(Icons.delete_outline),
                    onPressed: () => removeNoteContent(i),
                  ),
                ),
              const SizedBox(height: 10),
              ElevatedButton.icon(
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 20),
                  backgroundColor: ColorHelper.thePunchRed(),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                onPressed: addNote,
                icon: const Icon(Icons.add_outlined, color: Colors.white),
                label: const Text(
                  'Add New Note',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
            backgroundColor: Colors.grey[300],
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
          onPressed: () => Navigator.of(context).pop(),
          child: const Text(
            'Cancel',
            style: TextStyle(
              color: Colors.black87,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
            backgroundColor: ColorHelper.thePunchRed(),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
          onPressed: () {
            widget.onSave(notes);
            Navigator.of(context).pop();
          },
          child: const Text(
            'Save',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
}

/// The ViewModel that manages editing a location. Uses ChangeNotifier.
class EditLocationViewModel extends ChangeNotifier {
  final String locationId;
  my_app.Location? location;
  List<LocationNote> locationNotes = [];
  bool _isInitializing = true;

  // Controllers for note text preview & address fields:
  final notesController = TextEditingController();
  final address1Controller = TextEditingController();
  final address2Controller = TextEditingController();
  final cityController = TextEditingController();
  final stateController = TextEditingController();
  final zipController = TextEditingController();

  // For the geo fence radius display (read-only):
  final geoFenceController = TextEditingController(text: '');

  // Google Map data:
  GoogleMapController? mapController;
  LatLng? mapCenter;
  LatLng? radiusHandle;
  BitmapDescriptor? radiusHandleIcon;
  bool circleSelected = false;

  bool _isLoading = false;
  Timer? _debounce;

  bool get isLoading => _isLoading;

  EditLocationViewModel(this.locationId) {
    _refresh();
    // Commented out to avoid auto-geocoding from address:
    // _setupListeners();
  }

  void onCenterMarkerDragEnd(LatLng newLatLng) {
    if (location == null) return;

    // Overwrite the location’s lat/long.
    location!.latitude = newLatLng.latitude;
    location!.longitude = newLatLng.longitude;
    location!.isDirty = true;

    // Update the mapCenter to this new position.
    mapCenter = newLatLng;

    // Recalc the radius handle so it stays the correct distance from the center.
    _initRadiusHandle();

    notifyListeners();
  }

  /// Fetch location & notes from local DB, then set mapCenter from lat/long.
  Future<void> _refresh() async {
    if (locationId.isEmpty) {
      // New location:
      location = my_app.Location.create();
    } else {
      // Existing location:
      location = await DataModel().locationModel.getById(locationId);
      locationNotes =
          await DataModel().locationNoteModel.getByLocationIds([locationId]);
      locationNotes = locationNotes.where((n) => n.isActive).toList();
      updateNotesText();
      _setAddressFields();
    }

    if (location != null) {
      final lat = location!.latitude;
      final lng = location!.longitude;
      // Use lat/long from DB, if non-zero:
      if (lat != 0.0 && lng != 0.0) {
        mapCenter = LatLng(lat, lng);
      } else {
        // If you want a default or do nothing:
        // mapCenter = LatLng(37.7749, -122.4194); // SF, or just do nothing
        // location!.latitude = 37.7749;
        // location!.longitude = -122.4194;
      }

      // Ensure we have a non-zero fence radius:
      if (location!.geoFenceRadius <= 0) {
        location!.geoFenceRadius = 100.0;
      }

      _initRadiusHandle();
      _updateGeoFenceText();

      await updateRadiusMarkerIcon();
    }

    notifyListeners();
    _isInitializing = false;
  }

  Future<void> updateRadiusMarkerIcon() async {
    if (mapCenter == null || location == null) return;

    final meters = location!.geoFenceRadius.toStringAsFixed(0);
    final icon = await _createCustomMarkerBitmap(
      "Adjust Geo Fence ($meters m)",
      fontSize: 12,
      textColor: Colors.white,
      backgroundColor: Colors.blueAccent,
    );

    setRadiusHandleIcon(icon);
  }

  // Commented out to disable auto-geocode from address fields
  /*
  void _setupListeners() {
    address1Controller.addListener(startDelayedUpdateMapCenter);
    address2Controller.addListener(startDelayedUpdateMapCenter);
    cityController.addListener(startDelayedUpdateMapCenter);
    stateController.addListener(startDelayedUpdateMapCenter);
    zipController.addListener(startDelayedUpdateMapCenter);
  }

  void startDelayedUpdateMapCenter() {
    _debounce?.cancel();
    _debounce = Timer(const Duration(seconds: 1), _checkAndUpdateMap);
  }

  Future<void> _checkAndUpdateMap() async {
    // If you did want to geocode after user types an address,
    // call _updateMapCenter() here. Currently disabled.
  }

  Future<void> _updateMapCenter() async {
    // Example of geocoding if you want it:
    // final geocodeData = await ApiModel().fetchGeoCodeData(
    //   address1: address1Controller.text,
    //   city: cityController.text,
    //   state: stateController.text,
    //   zip: zipController.text,
    // );
    // if (geocodeData.isNotEmpty) {
    //   final lat = geocodeData['Latitude'] as double;
    //   final lng = geocodeData['Longitude'] as double;
    //   mapCenter = LatLng(lat, lng);
    //   location!.latitude = lat;
    //   location!.longitude = lng;
    //   location!.isDirty = true;
    //   if (mapController != null) {
    //     mapController!.animateCamera(CameraUpdate.newLatLng(mapCenter!));
    //   }
    // }
  }
  */

  void _initRadiusHandle() {
    if (location == null || mapCenter == null) return;
    final meters = location!.geoFenceRadius;
    final offsetInDegrees = meters / 111111.0;
    radiusHandle = LatLng(
      mapCenter!.latitude + offsetInDegrees,
      mapCenter!.longitude,
    );
  }

  Future<void> onRadiusMarkerDrag(LatLng newLatLng) async {
    if (location == null || mapCenter == null) return;

    final dist = geo.Geolocator.distanceBetween(
      mapCenter!.latitude,
      mapCenter!.longitude,
      newLatLng.latitude,
      newLatLng.longitude,
    );

    location!.geoFenceRadius = dist.ceilToDouble();
    location!.isDirty = true;
    radiusHandle = newLatLng;

    _updateGeoFenceText();
    await updateRadiusMarkerIcon();

    notifyListeners();
  }

  void setCircleSelected(bool value) {
    circleSelected = value;
    notifyListeners();
  }

  void _updateGeoFenceText() {
    if (location == null) return;
    geoFenceController.text = location!.geoFenceRadius.toStringAsFixed(0);
  }

  void setMapController(GoogleMapController controller) {
    mapController = controller;
  }

  void setRadiusHandleIcon(BitmapDescriptor icon) {
    radiusHandleIcon = icon;
    notifyListeners();
  }

  Future<void> saveAndSync() async {
    try {
      _setLoading(true);
      if (!canSave) throw 'No changes to save or invalid input.';

      await _saveLocation();
      await _saveLocationNotes();

      // Post to server + do a sync:
      await ApiModel().postChanges(
        locations: [location!],
        locationNotes: locationNotes,
      );
      await SyncModel().sync();

      debugPrint('Location & notes saved and synced successfully.');
    } catch (e) {
      debugPrint('Failed saving/syncing: $e');
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool val) {
    _isLoading = val;
    notifyListeners();
  }

  Future<void> _saveLocation() async {
    if (location == null || !location!.isDirty) return;
    if (location!.id.isEmpty) {
      location!.id = const Uuid().v4();
    }
    await DataModel().locationModel.save([location!]);
    location!.isDirty = false;
  }

  Future<void> _saveLocationNotes() async {
    if (location == null) return;
    for (final note in locationNotes) {
      note.locationId = location!.id;
      note.isDirty = true;
    }

    final dirtyNotes = locationNotes.where((n) => n.isDirty).toList();
    await DataModel().locationNoteModel.saveDirty(dirtyNotes);
    for (final note in dirtyNotes) {
      note.isDirty = false;
    }
  }

  bool get canSave {
    final loc = location;
    if (loc == null) return false;

    // Basic validation checks:
    if (loc.name.isEmpty ||
        loc.phone.isEmpty ||
        loc.emailAddress.isEmpty ||
        loc.address1.isEmpty ||
        loc.city.isEmpty ||
        loc.state.isEmpty ||
        loc.zip.isEmpty ||
        loc.timeZone.isEmpty) {
      return false;
    }

    return loc.isDirty || locationNotes.any((n) => n.isDirty);
  }

  // Basic setters:
  void setName(String val) {
    final loc = location;
    if (loc == null) return;
    loc.name = val;
    loc.isDirty = true;
    notifyListeners();
  }

  void setPhone(String val) {
    final loc = location;
    if (loc == null) return;
    loc.phone = val;
    loc.isDirty = true;
    notifyListeners();
  }

  void setEmail(String val) {
    final loc = location;
    if (loc == null) return;
    loc.emailAddress = val;
    loc.isDirty = true;
    notifyListeners();
  }

  void setActive(bool val) {
    final loc = location;
    if (loc == null) return;
    loc.isActive = val;
    loc.isDirty = true;
    notifyListeners();
  }

  void setAddress1(String val) {
    final loc = location;
    if (loc == null) return;
    loc.address1 = val;
    loc.isDirty = true;
    notifyListeners();
  }

  void setAddress2(String val) {
    final loc = location;
    if (loc == null) return;
    loc.address2 = val;
    loc.isDirty = true;
    notifyListeners();
  }

  void setCity(String val) {
    final loc = location;
    if (loc == null) return;
    loc.city = val;
    loc.isDirty = true;
    notifyListeners();
  }

  void setState(String val) {
    final loc = location;
    if (loc == null) return;
    loc.state = val;
    loc.isDirty = true;
    notifyListeners();
  }

  void setZip(String val) {
    final loc = location;
    if (loc == null) return;
    loc.zip = val;
    loc.isDirty = true;
    notifyListeners();
  }

  void setTimeZone(String val) {
    final loc = location;
    if (loc == null) return;
    loc.timeZone = val;
    loc.isDirty = true;
    notifyListeners();
  }

  Future<void> setLocationNotes(List<LocationNote> updatedNotes) async {
    final notesToDelete = locationNotes
        .where((oldNote) =>
            !updatedNotes.any((newNote) => newNote.id == oldNote.id))
        .toList();
    for (var note in notesToDelete) {
      note.isActive = false;
      note.isDirty = true;
    }

    locationNotes = updatedNotes.where((n) => n.isActive).toList();
    for (var note in locationNotes) {
      note.isDirty = true;
    }

    updateNotesText();

    await DataModel().locationNoteModel.save(notesToDelete);
    await DataModel().locationNoteModel.save(locationNotes);

    notifyListeners();
  }

  void updateNotesText() {
    final activeNotes = locationNotes.where((n) => n.isActive).toList();
    final formatted = activeNotes.map((n) => '• ${n.note}').join('\n');
    notesController.text = formatted;
  }

  void _setAddressFields() {
    if (location == null) return;
    address1Controller.text = location!.address1;
    address2Controller.text = location!.address2;
    cityController.text = location!.city;
    stateController.text = location!.state;
    zipController.text = location!.zip;
  }
}
