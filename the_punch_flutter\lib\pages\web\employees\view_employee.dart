import 'dart:async';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:provider/provider.dart';
import '../../../dataModel/data/user.dart';
import '../../../dataModel/data/user_type.dart';
import '../../../dataModel/data_model.dart';
import '../../../dialogs/material_dialog.dart';
import '../../../helpers/color_helper.dart';
import '../../../misc/app_localization.dart';
import '../../../misc/change_notification_builder.dart';
import '../../../widgets/tables_global.dart';
import '../../view_model_mixin.dart';
import '../my_scaffold.dart';
import 'employee_mixin.dart';
import '../../../misc/extensions.dart';
import '../../../state/login_state.dart';
import '../../../state/permissions_state.dart';
import '../../../widgets/alerts_widget.dart';
import '../../../widgets/chat_widget.dart';
import '../../../widgets/decorated_text_field.dart';
import '../../../widgets/notes_widget.dart';
import '../../../widgets/punch_cards_widget.dart';
import '../../../widgets/schedules_widget.dart';
import '../../../widgets/menu/my_tab_bar.dart';
import '../../../dialogs/remove_dialog.dart';
import '../../../dataModel/data/location.dart';
import '../../../dataModel/data/location_contact.dart';
import '../../../dialogs/web/expanded_locations_dialog.dart';

class ViewEmployeePage extends StatelessWidget {
  final String employeeId;

  ViewEmployeePage(Map<String, String> queryParms, {super.key})
      : employeeId = queryParms['id'] ?? '';

  @override
  Widget build(BuildContext context) => ChangeNotifierBuilder<_ViewModel>(
        create: (context) => _ViewModel(employeeId),
        builder: (context, viewModel, child) => MyScaffold(
          title: AppLocalization.of(context).employee,
          body: _Body(),
        ),
      );
}

class _Body extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    // Determine screen width for responsive layouts
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 600;
    final userId = context.select<_ViewModel, String>((v) => v.employeeId);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _BodyHeader(),
          const SizedBox(height: 25),
          Row(
            children: [
              Icon(
                Icons.person,
                size: 24,
                color: ColorHelper.thePunchDarkBlue(),
              ),
              const SizedBox(width: 8),
              Text(
                'Details',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w700,
                      fontSize: 20,
                      color: ColorHelper.thePunchDarkBlue(),
                    ),
              ),
            ],
          ),
          SizedBox(height: 10),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[100], // Light gray background
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                isMobile ? _ResponsiveColumn1() : _Row1(),
                const SizedBox(height: 10),
                isMobile
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          _ResponsiveColumn2(),
                        ],
                      )
                    : _Row2(),
                const SizedBox(height: 10),
                isMobile
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          _ResponsiveColumn3(),
                        ],
                      )
                    : _Row3(),
              ],
            ),
          ),
          const SizedBox(height: 75),
          _LocationsTab(),
          SizedBox(height: 10),
          NotesWidget(userId: userId)
        ],
      ),
    );
  }
}

class _Section extends StatelessWidget {
  final String title;
  final Widget child;

  const _Section({required this.title, required this.child, Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 600;
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w700,
                  fontSize: 20,
                  color: ColorHelper.thePunchDarkBlue(),
                ),
          ),
          const SizedBox(height: 20),
          isMobile
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [child],
                )
              : child,
        ],
      ),
    );
  }
}

class _BodyHeader extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    // Make the employee name at the top larger:
    final largerStyle = Theme.of(context).textTheme.headlineMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: Colors.grey[800],
        );

    return IntrinsicHeight(
      child: Consumer<_ViewModel>(
        builder: (context, viewModel, child) {
          final employee = viewModel.employee;
          if (employee == null) {
            return Center(
              child: Text(
                'There are no employees yet. Click "Add Employee" to add a new employee.',
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            );
          }
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Use the new larger style here
              Text(employee.name, style: largerStyle),
              const SizedBox(height: 20),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Wrap(
                  alignment: WrapAlignment.start,
                  spacing: 12,
                  runSpacing: 8,
                  children: [
                    // If user is viewing some other employee, show the "Chat" button
                    if (viewModel.employeeId != LoginState.userId)
                      _HeaderButton(
                        icon: Icons.message,
                        label: AppLocalization.of(context).chat,
                        onPressed: () => _showDialog(
                          context,
                          ChatWidget(
                            groupId: viewModel.employeeId,
                            groupName: employee.name,
                            // Pass the groupMemberUserIds from the ViewModel
                            groupMemberUserIds: viewModel.groupMemberUserIds,
                          ),
                        ),
                      ),
                    _HeaderButton(
                      icon: Icons.notifications,
                      label: AppLocalization.of(context).alerts,
                      onPressed: () => _showDialog(
                        context,
                        AlertsWidget(userId: viewModel.employeeId),
                      ),
                    ),
                    _HeaderButton(
                      icon: Icons.edit,
                      label: AppLocalization.of(context).notes,
                      onPressed: () => _showDialog(
                        context,
                        NotesWidget(userId: viewModel.employeeId),
                      ),
                    ),
                    _HeaderButton(
                      icon: Icons.timelapse,
                      label: AppLocalization.of(context).punchCards,
                      onPressed: () => _showDialog(
                        context,
                        PunchCardsWidget(employeeId: viewModel.employeeId),
                      ),
                    ),
                    _HeaderButton(
                      icon: Icons.dashboard,
                      label: AppLocalization.of(context).schedules,
                      onPressed: () => _showDialog(
                        context,
                        SchedulesWidget(
                          employeeIds: [viewModel.employeeId],
                          showEmployeeNames: false,
                        ),
                      ),
                    ),
                    if (PermissionsState().editEmployees)
                      _HeaderButton(
                        icon: Icons.edit_note_outlined,
                        label: AppLocalization.of(context).editEmployee,
                        onPressed: () async => context.pushNamed(
                          '/employees/edit',
                          queryParameters: {'id': employee.id},
                        ),
                      ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  void _showDialog(BuildContext context, Widget child) {
    showDialog(
      context: context,
      builder: (context) => MaterialDialog(
        child: child,
      ),
    );
  }
}

class _HeaderButton extends StatelessWidget {
  final IconData? icon;
  final String label;
  final VoidCallback onPressed;

  const _HeaderButton({
    this.icon,
    required this.label,
    required this.onPressed,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDesktop = MediaQuery.of(context).size.width >= 1200;

    return ElevatedButton.icon(
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        elevation: isDesktop ? 4 : 2,
        backgroundColor: ColorHelper.thePunchAdminButtonBlue(),
        foregroundColor: Colors.white,
        surfaceTintColor: Colors.transparent,
        shadowColor: Colors.transparent,
      ),
      icon: icon != null
          ? Icon(icon, size: 20, color: Colors.white)
          : Icon(Icons.edit, size: 20, color: Colors.white),
      label: Text(
        label,
        style: Theme.of(context).textTheme.labelLarge?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
      ),
      onPressed: onPressed,
    );
  }
}

class _Row1 extends StatelessWidget {
  @override
  Widget build(BuildContext context) => IntrinsicHeight(
        child: Consumer<_ViewModel>(
          builder: (context, viewModel, child) {
            final employee = viewModel.employee;
            final userType = viewModel.userType;
            if (employee == null || userType == null) return Container();
            return Row(
              children: [
                Flexible(
                  flex: 20,
                  child: DecoratedText(
                    padding: const EdgeInsets.all(12),
                    text: employee.firstName,
                    labelText: AppLocalization.of(context).firstName,
                  ),
                ),
                Flexible(
                  flex: 20,
                  child: DecoratedText(
                    padding: const EdgeInsets.all(12),
                    text: employee.lastName,
                    labelText: AppLocalization.of(context).lastName,
                  ),
                ),
                Flexible(
                  flex: 10,
                  child: _buildEmployeeTypeDropdown(context, viewModel),
                ),
                Flexible(
                  flex: 10,
                  child: _buildStatusDropdown(context, viewModel),
                ),
              ],
            );
          },
        ),
      );

  Widget _buildEmployeeTypeDropdown(
      BuildContext context, _ViewModel viewModel) {
    final employee = viewModel.employee;
    final userType = viewModel.userType;
    final allUserTypes = viewModel.allUserTypes;

    if (employee == null || userType == null || allUserTypes.isEmpty) {
      return DecoratedText(
        padding: const EdgeInsets.all(12),
        text: userType?.name ?? '',
        labelText: AppLocalization.of(context).employeeType,
      );
    }

    return Padding(
        padding: const EdgeInsets.all(12),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Text(
            AppLocalization.of(context).employeeType,
            style: const TextStyle(color: Colors.black, fontSize: 12),
          ),
          SizedBox(height: 2),
          DropdownButtonFormField<String>(
            value: employee.userTypeId,
            decoration: InputDecoration(
              filled: true,
              fillColor: ColorHelper.thePunchDarkBlue(),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: ColorHelper.thePunchDarkBlue()),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: ColorHelper.thePunchDarkBlue()),
              ),
            ),
            dropdownColor: ColorHelper.thePunchDarkBlue(),
            style: const TextStyle(color: Colors.white),
            items: allUserTypes
                .map((UserType type) => DropdownMenuItem<String>(
                      value: type.id,
                      child: Text(type.name,
                          style: const TextStyle(color: Colors.white)),
                    ))
                .toList(),
            onChanged: (value) async {
              if (value != null && value != employee.userTypeId) {
                await viewModel.updateEmployeeType(value);
              }
            },
          )
        ]));
  }
}

Widget _buildStatusDropdown(BuildContext context, _ViewModel viewModel) {
  final employee = viewModel.employee;

  if (employee == null) {
    return DecoratedText(
      padding: const EdgeInsets.all(12),
      text: '',
      labelText: AppLocalization.of(context).activate,
    );
  }

  return Padding(
    padding: const EdgeInsets.all(12),
    child: DropdownButtonFormField<bool>(
      value: employee.isActive,
      decoration: InputDecoration(
        filled: true,
        fillColor: ColorHelper.thePunchDarkBlue(),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: ColorHelper.thePunchDarkBlue()),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: ColorHelper.thePunchDarkBlue()),
        ),
      ),
      dropdownColor: ColorHelper.thePunchDarkBlue(),
      style: const TextStyle(color: Colors.white),
      items: [
        DropdownMenuItem<bool>(
          value: true,
          child: Text(true.toActive(context)),
        ),
        DropdownMenuItem<bool>(
          value: false,
          child: Text(false.toActive(context)),
        ),
      ],
      onChanged: (value) async {
        if (value != null && value != employee.isActive) {
          await viewModel.updateEmployeeStatus(value);
        }
      },
    ),
  );
}

class _ResponsiveColumn1 extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Consumer<_ViewModel>(
        builder: (context, viewModel, child) {
          final employee = viewModel.employee;
          final userType = viewModel.userType;
          if (employee == null || userType == null) return Container();
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              DecoratedText(
                padding: const EdgeInsets.all(12),
                text: employee.firstName,
                labelText: AppLocalization.of(context).firstName,
              ),
              const SizedBox(height: 12),
              DecoratedText(
                padding: const EdgeInsets.all(12),
                text: employee.lastName,
                labelText: AppLocalization.of(context).lastName,
              ),
              const SizedBox(height: 12),
              DecoratedText(
                padding: const EdgeInsets.all(12),
                text: employee.username ?? '',
                labelText: 'Username',
              ),
              const SizedBox(height: 12),
              _buildEmployeeTypeDropdown(context, viewModel),
            ],
          );
        },
      );

  Widget _buildEmployeeTypeDropdown(
      BuildContext context, _ViewModel viewModel) {
    final employee = viewModel.employee;
    final userType = viewModel.userType;
    final allUserTypes = viewModel.allUserTypes;

    if (employee == null || userType == null || allUserTypes.isEmpty) {
      return DecoratedText(
        padding: const EdgeInsets.all(12),
        text: userType?.name ?? '',
        labelText: AppLocalization.of(context).employeeType,
      );
    }

    return Padding(
      padding: const EdgeInsets.all(12),
      child: DropdownButtonFormField<String>(
        value: employee.userTypeId,
        decoration: InputDecoration(
          labelText: AppLocalization.of(context).employeeType,
          filled: true,
          fillColor: Colors.white,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
        ),
        items: allUserTypes
            .map((UserType type) => DropdownMenuItem<String>(
                  value: type.id,
                  child: Text(type.name),
                ))
            .toList(),
        onChanged: (value) async {
          if (value != null && value != employee.userTypeId) {
            await viewModel.updateEmployeeType(value);
          }
        },
      ),
    );
  }
}

class _Row2 extends StatelessWidget {
  @override
  Widget build(BuildContext context) => IntrinsicHeight(
        child: Consumer<_ViewModel>(
          builder: (context, viewModel, child) {
            final employee = viewModel.employee;
            if (employee == null) return Container();
            return Row(
              children: [
                Flexible(
                  flex: 1,
                  child: DecoratedText(
                    padding: const EdgeInsets.all(12),
                    text: employee.phone ?? '',
                    labelText: AppLocalization.of(context).phone,
                  ),
                ),
                Flexible(
                  flex: 1,
                  child: DecoratedText(
                    padding: const EdgeInsets.all(12),
                    text: employee.emailAddress ?? '',
                    labelText: AppLocalization.of(context).emailAddress,
                  ),
                ),
                Flexible(
                  flex: 1,
                  child: DecoratedText(
                    padding: const EdgeInsets.all(12),
                    text: employee.payRate == null
                        ? ''
                        : '${employee.payRate!.toCurrency} ${AppLocalization.translatePayRateFrequency(context, employee.payRateFrequency)}',
                    labelText: AppLocalization.of(context).payRate,
                  ),
                ),
              ],
            );
          },
        ),
      );

  Widget _buildStatusDropdown(BuildContext context, _ViewModel viewModel) {
    final employee = viewModel.employee;

    if (employee == null) {
      return DecoratedText(
        padding: const EdgeInsets.all(12),
        text: '',
        labelText: AppLocalization.of(context).activate,
      );
    }

    return Padding(
      padding: const EdgeInsets.all(12),
      child: DropdownButtonFormField<bool>(
        value: employee.isActive,
        decoration: InputDecoration(
          filled: true,
          fillColor: ColorHelper.thePunchDarkBlue(),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: ColorHelper.thePunchDarkBlue()),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: ColorHelper.thePunchDarkBlue()),
          ),
        ),
        dropdownColor: ColorHelper.thePunchDarkBlue(),
        style: const TextStyle(color: Colors.white),
        items: [
          DropdownMenuItem<bool>(
            value: true,
            child: Text(true.toActive(context)),
          ),
          DropdownMenuItem<bool>(
            value: false,
            child: Text(false.toActive(context)),
          ),
        ],
        onChanged: (value) async {
          if (value != null && value != employee.isActive) {
            await viewModel.updateEmployeeStatus(value);
          }
        },
      ),
    );
  }
}

class _ResponsiveColumn2 extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Consumer<_ViewModel>(
        builder: (context, viewModel, child) {
          final employee = viewModel.employee;
          if (employee == null) return Container();
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              DecoratedText(
                padding: const EdgeInsets.all(12),
                text: employee.phone ?? '',
                labelText: AppLocalization.of(context).phone,
              ),
              const SizedBox(height: 12),
              DecoratedText(
                padding: const EdgeInsets.all(12),
                text: employee.emailAddress ?? '',
                labelText: AppLocalization.of(context).emailAddress,
              ),
              const SizedBox(height: 12),
              DecoratedText(
                padding: const EdgeInsets.all(12),
                text: employee.payRate == null
                    ? ''
                    : '${employee.payRate!.toCurrency} ${AppLocalization.translatePayRateFrequency(context, employee.payRateFrequency)}',
                labelText: AppLocalization.of(context).payRate,
              ),
              const SizedBox(height: 12),
              _buildStatusDropdown(context, viewModel),
            ],
          );
        },
      );

  Widget _buildStatusDropdown(BuildContext context, _ViewModel viewModel) {
    final employee = viewModel.employee;

    if (employee == null) {
      return DecoratedText(
        padding: const EdgeInsets.all(12),
        text: '',
        labelText: AppLocalization.of(context).activate,
      );
    }

    return Padding(
      padding: const EdgeInsets.all(12),
      child: DropdownButtonFormField<bool>(
        value: employee.isActive,
        decoration: InputDecoration(
          labelText: AppLocalization.of(context).status,
          filled: true,
          fillColor: Colors.white,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
        ),
        items: [
          DropdownMenuItem<bool>(
            value: true,
            child: Text(true.toActive(context)),
          ),
          DropdownMenuItem<bool>(
            value: false,
            child: Text(false.toActive(context)),
          ),
        ],
        onChanged: (value) async {
          if (value != null && value != employee.isActive) {
            await viewModel.updateEmployeeStatus(value);
          }
        },
      ),
    );
  }
}

class _Row3 extends StatelessWidget {
  @override
  Widget build(BuildContext context) => IntrinsicHeight(
        child: Consumer<_ViewModel>(
          builder: (context, viewModel, child) {
            final employee = viewModel.employee;
            if (employee == null) return Container();

            return Row(
              children: [
                Flexible(
                  flex: 1,
                  child: DecoratedText(
                    padding: const EdgeInsets.all(12),
                    text: AppLocalization.localeName(
                        context, employee.languageKey),
                    labelText: 'Language',
                  ),
                ),
              ],
            );
          },
        ),
      );
}

class _ResponsiveColumn3 extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Consumer<_ViewModel>(
        builder: (context, viewModel, child) {
          final employee = viewModel.employee;
          if (employee == null) return Container();
          return DecoratedText(
            padding: const EdgeInsets.all(12),
            text: AppLocalization.localeName(context, employee.languageKey),
            labelText: 'Language',
          );
        },
      );
}

class _TabBar extends StatelessWidget {
  const _TabBar();

  @override
  Widget build(BuildContext context) {
    final userId = context.select<_ViewModel, String>((v) => v.employeeId);
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 600;

    return SizedBox(
      height: isMobile ? 400 : 600,
      child: MyTabBar(
        itemBuilder: (context, value) {
          switch (value) {
            case 0:
              return _LocationsTab();
            case 1:
              return NotesWidget(userId: userId);
            default:
              return const Placeholder();
          }
        },
        children: [
          TabButton(
            value: 0,
            child: Row(
              children: [
                const Icon(Icons.location_on,
                    size: 20, color: Colors.redAccent),
                const SizedBox(width: 8),
                Text(AppLocalization.of(context).locations),
              ],
            ),
          ),
          TabButton(
            value: 1,
            child: Row(
              children: [
                const Icon(Icons.note, size: 20, color: Colors.redAccent),
                const SizedBox(width: 8),
                Text(AppLocalization.of(context).notes),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _LocationsTab extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final locationIds =
        context.select<_ViewModel, Iterable<String>>((v) => v.locationIds);
    final addLocationId =
        context.select<_ViewModel, Function(String id)>((v) => v.addLocationId);

    final columns = [
      DataColumn(label: Text(AppLocalization.of(context).locationName)),
      DataColumn(label: Text(AppLocalization.of(context).address)),
      DataColumn(label: Text(AppLocalization.of(context).city)),
      DataColumn(label: Text(AppLocalization.of(context).state)),
      DataColumn(label: Text(AppLocalization.of(context).phone)),
      DataColumn(
          label: Expanded(
              child: Center(child: Text(AppLocalization.of(context).active)))),
      DataColumn(label: Text(AppLocalization.of(context).actions)),
    ];
    final rows = _getCustomTableRows(context);

    return Padding(
      // Remove extra Card or Container with colors if you don't want a background
      padding: const EdgeInsets.all(0),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.location_on_outlined,
                    size: 24,
                    color: ColorHelper.thePunchDarkBlue(),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Locations',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w700,
                          fontSize: 20,
                          color: ColorHelper.thePunchDarkBlue(),
                        ),
                  ),
                ],
              ),
              ElevatedButton.icon(
                onPressed: () => unawaited(showDialog(
                  context: context,
                  builder: (context) => ExpandedLocationsDialog(
                    onSelection: addLocationId,
                    ignoreLocationIds: locationIds,
                  ),
                )),
                icon: const Icon(Icons.add_location_alt, color: Colors.white),
                label: Text(AppLocalization.of(context).addEmployeeToLocation),
                style: ElevatedButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8)),
                  backgroundColor: ColorHelper.thePunchAdminButtonBlue(),
                ),
              )
            ],
          ),
          const SizedBox(height: 10),

          // Wrap table in Expanded + SingleChildScrollView to allow scrolling
          //  Expanded(
          SingleChildScrollView(
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(
                  color: ColorHelper.thePunchAdminButtonBlue().withOpacity(0.2),
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: CustomTable(
                columns: columns,
                rows: rows,
                onRowClick: (DataRow row) {},
              ),
            ),
          ),
          //  ),
          // FIND OUT WHAT THIS ISSSSSSSSSSSS!!!!
          // if (locationIds.isEmpty)
          //   Padding(
          //     padding: const EdgeInsets.symmetric(vertical: 20),
          //     child: Text(
          //       'This contact is not in any locations. Click "Add Contact to Location" above to add them to a location.',
          //       textAlign: TextAlign.center,
          //       style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          //             color: Colors.grey[600],
          //           ),
          //     ),
          //   ),
        ],
      ),
    );
  }

  List<DataRow> _getCustomTableRows(BuildContext context) {
    final locations =
        context.select<_ViewModel, Iterable<Location>>((v) => v.locations);
    final removeLocationId = context
        .select<_ViewModel, Function(String id)>((v) => v.removeLocationId);

    return locations
        .map((location) => DataRow(
              cells: [
                DataCell(Text(location.name)),
                DataCell(Text(location.address1)),
                DataCell(Text(location.city)),
                DataCell(Text(location.state)),
                DataCell(Text(location.phone)),
                DataCell(Center(
                    child: location.isActive
                        ? Image.asset('images/custom_location.png',
                            width: 24, height: 24)
                        : Icon(Symbols.where_to_vote, color: Colors.grey))),
                DataCell(Row(
                  children: [
                    IconButton(
                      icon: Icon(Icons.visibility,
                          color: ColorHelper.thePunchAdminButtonBlue()),
                      tooltip: AppLocalization.of(context).view,
                      onPressed: () async => context.pushNamed(
                        '/locations/view',
                        queryParameters: {'id': location.id},
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.remove_circle,
                          color: ColorHelper.thePunchAdminButtonBlue()),
                      tooltip: AppLocalization.of(context).remove,
                      onPressed: () => unawaited(showDialog(
                        context: context,
                        builder: (context) => RemoveDialog(
                          remove: () => removeLocationId(location.id),
                        ),
                      )),
                    ),
                  ],
                )),
              ],
            ))
        .toList();
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin, EmployeeMixin {
  final String employeeId;
  User? employee;
  UserType? userType;
  List<UserType> allUserTypes = [];
  Map<String, Location> locationMap = {};
  Iterable<LocationContact> locationContacts = [];
  Iterable<String> get locationIds => locationMap.keys;
  Iterable<Location> get locations => locationMap.values;
  bool isSelf = false;

  /// Add a Set to store all userIds that are part of this "group"
  Set<String> groupMemberUserIds = {};

  _ViewModel(this.employeeId) {
    addListenables([
      DataModel().userModel,
      DataModel().userTypeModel,
      DataModel().locationModel,
      DataModel().locationContactModel,
      // If your DataModel has groupMemberModel:
      DataModel().groupMemberModel,
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    // 1) Load user + userType
    employee = await DataModel().userModel.getById(employeeId);
    if (employee == null) return;

    // 2) Load all user types for dropdown
    allUserTypes = (await DataModel().userTypeModel.all).toList();

    userType =
        (await DataModel().userTypeModel.getByIds([employee!.userTypeId]))
            .firstOrNull;

    // 3) Load location contacts + location info
    locationContacts =
        await DataModel().locationContactModel.getByUserIds([employeeId]);
    final locIds = locationContacts.map((e) => e.locationId);
    final locs = await DataModel().locationModel.getByIds(locIds);
    locationMap = {for (final location in locs) location.id: location};

    // 4) (Optional) Mark if we're looking at our own user record
    isSelf = (LoginState.userId == employeeId);

    // 5) FETCH THE GROUP MEMBERS FOR THIS "employeeId" as the groupId
    try {
      final members =
          await DataModel().groupMemberModel.getByGroupId(employeeId);
      groupMemberUserIds = members.map((m) => m.userId).toSet();
    } catch (e) {
      // If there's no group or an error, fall back to empty set
      debugPrint('Error fetching group members for $employeeId: $e');
      groupMemberUserIds = {};
    }

    notifyListeners();
  }

  Future<void> updateEmployeeType(String userTypeId) async {
    if (employee == null) return;

    // Create a copy of the employee to modify
    final updatedEmployee = User.from(employee!);
    updatedEmployee.userTypeId = userTypeId;

    // Save the updated employee
    await DataModel().userModel.saveDirty([updatedEmployee]);

    // Update the local employee object
    employee = updatedEmployee;

    // Update the userType
    userType = allUserTypes.firstWhere(
      (type) => type.id == userTypeId,
      orElse: () => userType!,
    );

    notifyListeners();
  }

  Future<void> updateEmployeeStatus(bool isActive) async {
    if (employee == null) return;

    // Create a copy of the employee to modify
    final updatedEmployee = User.from(employee!);
    updatedEmployee.isActive = isActive;

    // Save the updated employee
    await DataModel().userModel.saveDirty([updatedEmployee]);

    // Update the local employee object
    employee = updatedEmployee;

    notifyListeners();
  }

  Future<void> addLocationId(String id) async {
    final locationContact = LocationContact.create()
      ..userId = employeeId
      ..locationId = id;
    await DataModel().locationContactModel.saveDirty([locationContact]);
    final location = await DataModel().locationModel.getById(id);
    if (location != null) {
      locationMap[id] = location;
    }
    notifyListeners();
  }

  Future<void> removeLocationId(String id) async {
    final locationContact =
        locationContacts.firstWhere((e) => e.locationId == id);
    locationContact.isActive = false;
    await DataModel().locationContactModel.saveDirty(locationContacts);
    locationMap.remove(id);
    notifyListeners();
  }
}
