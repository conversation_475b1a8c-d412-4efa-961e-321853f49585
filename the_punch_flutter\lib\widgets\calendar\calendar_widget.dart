import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../api/api_model.dart';
import '../../dataModel/data/schedule.dart';
import '../../dataModel/models/schedule_model.dart';
import '../../helpers/color_helper.dart';
import '../../misc/extensions.dart';
import '../../pages/view_model_mixin.dart';
import 'calendar_month_widget.dart';
import 'calendar_week_widget.dart';
import 'calendar_day_widget.dart';
import 'calendar_list_widget.dart';

enum CalendarState { month, week, day, list }

class CalendarWidget extends StatefulWidget {
  final dynamic selectedDate;
  final Future<Iterable<CalendarEvent>> Function(
      BuildContext context, DateTime start, DateTime end) builder;
  final bool scrollable;

  const CalendarWidget({
    super.key,
    required this.builder,
    this.scrollable = true,
    this.selectedDate,
  });

  @override
  State<CalendarWidget> createState() => _CalendarWidgetState(selectedDate);
}

class _CalendarWidgetState extends State<CalendarWidget> {
  dynamic selectedDate;

  final buttonStyle = OutlinedButton.styleFrom(
    side: BorderSide(
      color: ColorHelper.thePunchRed(),
    ),
  );

  _CalendarWidgetState(this.selectedDate);

  @override
  void initState() {
    super.initState();
    debugPrint(selectedDate.toString());
  }

  @override
  Widget build(BuildContext context) => Consumer<CalendarViewModel>(
      builder: (context, viewModel, _) => Column(
        children: [
          if (viewModel.isLoading)
            CircularProgressIndicator(
              color: ColorHelper.thePunchRed(),
            ),
          Expanded(
            child: Builder(
              builder: (context) {
                switch (viewModel.calendarState) {
                  case CalendarState.month:
                    return CalendarMonthWidget(
                      builder: widget.builder,
                      suffix: buildHeaderSuffix(viewModel),
                      scrollable: widget.scrollable,
                      height: CalendarViewModel.monthPageHeight,
                    );
                  case CalendarState.week:
                    return CalendarWeekWidget(
                      builder: widget.builder,
                      suffix: buildHeaderSuffix(viewModel),
                      scrollable: widget.scrollable,
                      height: CalendarViewModel.pageHeight,
                    );
                  case CalendarState.day:
                    return CalendarDayWidget(
                      builder: widget.builder,
                      suffix: buildHeaderSuffix(viewModel),
                      scrollable: widget.scrollable,
                      height: CalendarViewModel.pageHeight,
                    );
                  case CalendarState.list:
                    return CalendarListWidget(
                      builder: widget.builder,
                      suffix: buildHeaderSuffix(viewModel),
                      scrollable: widget.scrollable,
                      height: CalendarViewModel.pageHeight,
                    );
                  default:
                    return const Placeholder();
                }
              },
            ),
          ),
        ],
      ),
    );

  Widget buildHeaderSuffix(CalendarViewModel viewModel) => Row(
    children: [
      Padding(
        padding: const EdgeInsets.all(8),
        child: OutlinedButton(
          style: buttonStyle,
          onPressed: () => viewModel.setCalendarState(CalendarState.month),
          child: const Text('Month'),
        ),
      ),
      Padding(
        padding: const EdgeInsets.all(8),
        child: OutlinedButton(
          style: buttonStyle,
          onPressed: () => viewModel.setCalendarState(CalendarState.week),
          child: const Text('Week'),
        ),
      ),
      Padding(
        padding: const EdgeInsets.all(8),
        child: OutlinedButton(
          style: buttonStyle,
          onPressed: () => viewModel.setCalendarState(CalendarState.day),
          child: const Text('Day'),
        ),
      ),
    ],
  );
}

class CalendarHeaderPrefix extends StatelessWidget {
  final Function() previousPage;
  final Function() nextPage;
  final Function() today;

  CalendarHeaderPrefix({
    super.key,
    required this.previousPage,
    required this.nextPage,
    required this.today,
  });

  final buttonStyle = OutlinedButton.styleFrom(
    side: BorderSide(
      color: ColorHelper.thePunchRed(),
    ),
  );

  @override
  Widget build(BuildContext context) => Row(
    children: [
      Padding(
        padding: const EdgeInsets.all(8),
        child: OutlinedButton(
          style: buttonStyle,
          onPressed: today,
          child: const Text('Today'),
        ),
      ),
      Padding(
        padding: const EdgeInsets.only(left: 8, top: 8, bottom: 8),
        child: OutlinedButton(
          style: buttonStyle,
          onPressed: previousPage,
          child: const Icon(Icons.arrow_left),
        ),
      ),
      const SizedBox(
        width: 10,
      ),
      Padding(
        padding: const EdgeInsets.only(right: 8, top: 8, bottom: 8),
        child: OutlinedButton(
          style: buttonStyle,
          onPressed: nextPage,
          child: const Icon(Icons.arrow_right),
        ),
      ),

    ],
  );
}

class CalendarEvent {
  DateTime start;
  DateTime end;
  String text;
  String? detailedText;
  Function()? onTap;
  final PopupMenuItemBuilder<int>? popupItemBuilder;
  final PopupMenuItemSelected<int>? popupOnSelected;
  String? tooltip;
  Widget? prefixIcon;
  Widget? suffixIcon;
  Color? color;
  TextStyle? textStyle;
  TextStyle? tooltipTextStyle;

  DateTime splitStart;
  DateTime splitEnd;

  CalendarEvent({
    required this.start,
    required this.end,
    required this.text,
    this.detailedText,
    this.onTap,
    this.popupItemBuilder,
    this.popupOnSelected,
    this.tooltip,
    this.prefixIcon,
    this.suffixIcon,
    this.color,
    this.textStyle,
    this.tooltipTextStyle,
  })  : splitStart = start,
        splitEnd = end;

  factory CalendarEvent.from(CalendarEvent o) => CalendarEvent(
    start: o.start,
    end: o.end,
    text: o.text,
    detailedText: o.detailedText,
    onTap: o.onTap,
    popupItemBuilder: o.popupItemBuilder,
    popupOnSelected: o.popupOnSelected,
    tooltip: o.tooltip,
    prefixIcon: o.prefixIcon,
    suffixIcon: o.suffixIcon,
    color: o.color,
    textStyle: o.textStyle,
    tooltipTextStyle: o.tooltipTextStyle,
  );
}

class CalendarViewModel extends ChangeNotifier with ViewModelMixin {
  final bool hasSelectedDate;
  final DateTime selectedDate;
  CalendarState calendarState = CalendarState.month;
  DateTime date = DateTime.now().dateOnly;
  bool isLoading = false;

  static const pageHeight = 1500.0;
  static const monthPageHeight = 1000.0;

  CalendarViewModel(this.hasSelectedDate, this.selectedDate) {
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    notifyListeners();
  }

  void setDate(DateTime date) {
    if (this.date == date) return;
    this.date = date;
    notifyListeners();
  }

  void setCalendarState(CalendarState calendarState) {
    if (this.calendarState == calendarState) return;
    this.calendarState = calendarState;
    notifyListeners();
  }

  void setDay(DateTime date) {
    this.date = date;
    calendarState = CalendarState.day;
    notifyListeners();
  }

  Future<void> fetchAndUpdateCalendar(BuildContext context) async {
    final scheduleModel = Provider.of<ScheduleModel>(context, listen: false);

    isLoading = true;
    notifyListeners();

    try {
      // Calculate the start date (current date)
      final startDate = date.dateOnly;

      // Load data for 30 days from the current date
      final allSchedules = <Schedule>[];

      // We need to load data for each month that falls within our 30-day window
      var currentDate = startDate;
      final endDate = startDate.addDays(30); // 30 days from start date

      while (currentDate.isBefore(endDate)) {
        final monthYear = "${currentDate.year}-${currentDate.month.toString().padLeft(2, '0')}";

        // Check if this month is already loaded
        if (!await scheduleModel.isMonthLoaded(monthYear)) {
          final schedules = await ApiModel().getSchedulesForMonth(currentDate.month, currentDate.year);
          allSchedules.addAll(schedules);
          await scheduleModel.markMonthAsLoaded(monthYear);
        }

        // Move to the next month
        currentDate = DateTime(currentDate.year, currentDate.month + 1);
      }

      // Save all schedules
      if (allSchedules.isNotEmpty) {
        await scheduleModel.save(allSchedules);
      }
    } catch (e) {
      debugPrint('Failed to load data: $e');
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  Future<void> goToPreviousMonth(BuildContext context) async {
    date = DateTime(date.year, date.month - 1);
    await fetchAndUpdateCalendar(context);
    notifyListeners();
  }

  Future<void> goToNextMonth(BuildContext context) async {
    date = DateTime(date.year, date.month + 1);
    await fetchAndUpdateCalendar(context);
    notifyListeners();
  }

  Future<void> goToPreviousWeek(BuildContext context) async {
    date = date.addDays(-7);
    await fetchAndUpdateCalendar(context);
    notifyListeners();
  }

  Future<void> goToNextWeek(BuildContext context) async {
    date = date.addDays(7);
    await fetchAndUpdateCalendar(context);
    notifyListeners();
  }
}
