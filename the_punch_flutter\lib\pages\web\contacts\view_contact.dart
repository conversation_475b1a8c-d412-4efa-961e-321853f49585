// lib/pages/web/contacts/view_contact.dart

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../../../dataModel/data/location_contact.dart';
import '../../../dataModel/data/user.dart';
import '../../../dataModel/data/location.dart';
import '../../../dataModel/data_model.dart';
import '../../../dialogs/remove_dialog.dart';
import '../../../dialogs/web/expanded_locations_dialog.dart';
import '../../../helpers/color_helper.dart';
import '../../../misc/app_localization.dart';
import '../../../widgets/tables_global.dart';
import '../../view_model_mixin.dart';
import '../home/<USER>';
import '../my_scaffold.dart';
import 'contact_mixin.dart';
import '../../../misc/extensions.dart';
import '../../../state/permissions_state.dart';
// Removed the import for active_toggle.dart as it's no longer used
// import '../../../widgets/active_toggle.dart';
import '../../../widgets/decorated_text_field.dart';
import '../../../widgets/menu/my_tab_bar.dart';
import '../../../widgets/notes_widget.dart';

class ViewContactPage extends StatelessWidget {
  final String userId;

  ViewContactPage(Map<String, String> queryParms, {super.key})
      : userId = queryParms['id'] ?? '';

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider<_ViewModel>(
        create: (context) => _ViewModel(userId),
        child: MyScaffold(
          title: AppLocalization.of(context).contact,
          body: _Body(),
        ),
      );
}

class _Body extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 1300),
          child: Column(
            children: [
              _BodyHeader(),
              _Row1(),
              _Row2(),
              const _TabBar(),
            ],
          ),
        ),
      );
}

class _BodyHeader extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final isDesktop = MediaQuery.of(context).size.width >= 768;
    final headline6 = Theme.of(context)
        .textTheme
        .titleLarge
        ?.copyWith(fontWeight: FontWeight.bold);
    final contact = context.select<_ViewModel, User?>((v) => v.contact);
    if (contact == null) return Container();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Contact title
          Text(
            contact.name,
            style: headline6?.copyWith(
              fontSize: 24,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 20),

          // Controls (buttons and switch)
          Align(
            alignment: isDesktop
                ? Alignment.centerRight
                : Alignment.center, // Center on mobile, right-align on desktop
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (PermissionsState().editContacts)
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Customized Switch: Green when active, Red when inactive
                      Switch(
                        value: contact.isActive,
                        onChanged: (bool value) async {
                          await context
                              .read<_ViewModel>()
                              .setActive(contact, value);
                        },
                        activeColor: ColorHelper
                            .thePunchGreen(), // Thumb color when active
                        activeTrackColor:
                            Colors.white, // Track color when active
                        inactiveThumbColor: ColorHelper
                            .thePunchRed(), // Thumb color when inactive
                        inactiveTrackColor:
                            Colors.white, // Track color when inactive
                      ),
                      const SizedBox(width: 8),
                      Text(
                        AppLocalization.of(context).activate,
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.black54,
                        ),
                      ),
                    ],
                  ),
                const SizedBox(width: 12),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 12),
                    backgroundColor: Colors.blueAccent,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  onPressed: () => context.pushNamed(
                    '/contacts/edit',
                    queryParameters: {'id': contact.id},
                  ),
                  child: Text(
                    AppLocalization.of(context).editContact,
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 12),
                    backgroundColor: Colors.redAccent,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(
                    AppLocalization.of(context).cancel,
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// Row 1 with responsive layout
class _Row1 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final contact = context.select<_ViewModel, User?>((v) => v.contact);
    if (contact == null) return Container();

    return LayoutBuilder(
      builder: (context, constraints) {
        final isMobile = constraints.maxWidth < 600;

        return Flex(
          direction: isMobile ? Axis.vertical : Axis.horizontal,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Flexible(
              flex: isMobile ? 0 : 1,
              child: DecoratedText(
                padding: const EdgeInsets.all(8),
                text: contact.firstName,
                labelText: AppLocalization.of(context).firstName,
              ),
            ),
            Flexible(
              flex: isMobile ? 0 : 1,
              child: DecoratedText(
                padding: const EdgeInsets.all(8),
                text: contact.lastName,
                labelText: AppLocalization.of(context).lastName,
              ),
            ),
          ],
        );
      },
    );
  }
}

// Row 2 with responsive layout
class _Row2 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final contact = context.select<_ViewModel, User?>((v) => v.contact);
    if (contact == null) return Container();

    return LayoutBuilder(
      builder: (context, constraints) {
        final isMobile = constraints.maxWidth < 600;

        return Flex(
          direction: isMobile ? Axis.vertical : Axis.horizontal,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Flexible(
              flex: isMobile ? 0 : 2,
              child: DecoratedText(
                padding: const EdgeInsets.all(8),
                text: contact.phone ?? '',
                labelText: AppLocalization.of(context).phone,
              ),
            ),
            Flexible(
              flex: isMobile ? 0 : 3,
              child: DecoratedText(
                padding: const EdgeInsets.all(8),
                text: contact.emailAddress ?? '',
                labelText: AppLocalization.of(context).emailAddress,
              ),
            ),
            Flexible(
              flex: isMobile ? 0 : 1,
              child: DecoratedText(
                padding: const EdgeInsets.all(8),
                text: contact.isActive.toActive(context),
                labelText: AppLocalization.of(context).activate,
              ),
            ),
          ],
        );
      },
    );
  }
}

class _TabBar extends StatelessWidget {
  const _TabBar();

  @override
  Widget build(BuildContext context) {
    final userId = context.select<_ViewModel, String>((v) => v.userId);

    return Expanded(
      child: MyTabBar(
        itemBuilder: (context, value) {
          switch (value) {
            case 0:
              return _LocationsTab();
            case 1:
              return NotesWidget(userId: userId);
            default:
              return const Placeholder();
          }
        },
        children: [
          TabButton(
            value: 0,
            child: Row(
              children: [
                Text(
                  AppLocalization.of(context).locations,
                  style: const TextStyle(color: Colors.black),
                ),
              ],
            ),
          ),
          TabButton(
            value: 1,
            child: Row(
              children: [
                Text(
                  AppLocalization.of(context).notes,
                  style: const TextStyle(color: Colors.black),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _LocationsTab extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final locationIds =
        context.select<_ViewModel, Iterable<String>>((v) => v.locationIds);
    final addLocationId =
        context.select<_ViewModel, Function(String id)>((v) => v.addLocationId);
    final removeLocationId = context
        .select<_ViewModel, Function(String id)>((v) => v.removeLocationId);
    final locations =
        context.select<_ViewModel, Iterable<Location>>((v) => v.locations);

    return Card(
      elevation: 0,
      child: Column(
        children: [
          Align(
            alignment: Alignment.centerRight,
            child: ElevatedButton(
              onPressed: () => unawaited(showDialog(
                context: context,
                builder: (context) => ExpandedLocationsDialog(
                  onSelection: addLocationId,
                  ignoreLocationIds: locationIds,
                ),
              )),
              child: Text(AppLocalization.of(context).addContactToLocation),
            ),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: CustomTable(
                columns: getColumns(context),
                rows: getRows(context, removeLocationId),
                onRowClick: (DataRow row) {
                  // Optionally define a default row click action
                },
                mobileTableTitle: "View Contacts",

                headerHeight: 56,
                rowHeight:
                    56, // Ensure this matches the button's height plus padding
                stickyHeader: true,
                headerFontSize: 16,
                cellFontSize: 14,
                columnWidths: const {
                  0: 0.18, // Location Name
                  1: 0.18, // Address
                  2: 0.15, // City
                  3: 0.1, // State
                  4: 0.15, // Phone
                  5: 0.1, // Active
                  6: 0.14, // Remove Action
                },
              ),
            ),
          ),
          if (locationIds.isEmpty)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'This contact is not in any locations. Click "Add Contact to Location" above to add them to a location.',
                textAlign: TextAlign.center,
              ),
            ),
        ],
      ),
    );
  }

  List<DataColumn> getColumns(BuildContext context) => [
        DataColumn(label: Text(AppLocalization.of(context).locationName)),
        DataColumn(label: Text(AppLocalization.of(context).address)),
        DataColumn(label: Text(AppLocalization.of(context).city)),
        DataColumn(label: Text(AppLocalization.of(context).state)),
        DataColumn(label: Text(AppLocalization.of(context).phone)),
        DataColumn(label: Text(AppLocalization.of(context).active)),
        DataColumn(
            label:
                Text(AppLocalization.of(context).remove)), // Only Remove Column
      ];

  List<DataRow> getRows(
      BuildContext context, Function(String id) removeLocationId) {
    final locations =
        context.select<_ViewModel, Iterable<Location>>((v) => v.locations);

    return locations
        .map((location) => DataRow(
              cells: [
                DataCell(Text(location.name)),
                DataCell(Text(location.address1)),
                DataCell(Text(location.city)),
                DataCell(Text(location.state)),
                DataCell(Text(location.phone)),
                DataCell(Text(location.isActive.toActive(context))),
                DataCell(
                  Center(
                    child: SizedBox(
                      width: 80, // Larger width for the button
                      height: 40, // Height to keep button visually balanced
                      child: ElevatedButton(
                        onPressed: () => unawaited(showDialog(
                          context: context,
                          builder: (context) => RemoveDialog(
                            remove: () {
                              removeLocationId(location.id);
                              Navigator.of(context).pop(); // Close the dialog
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                      '${location.name} has been removed from the contact.'),
                                  duration: const Duration(seconds: 3),
                                ),
                              );
                            },
                          ),
                        )),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.redAccent,
                          padding: EdgeInsets
                              .zero, // Remove padding for centered icon
                          minimumSize:
                              const Size(40, 40), // Adjusted size for balance
                          alignment: Alignment.center, // Center-align icon
                        ),
                        child: const Icon(Icons.remove_circle,
                            color: Colors.white),
                      ),
                    ),
                  ),
                ),
              ],
            ))
        .toList();
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin, ContactMixin {
  final String userId;
  User? contact;
  Map<String, Location> locationMap = {};

  Iterable<LocationContact> locationContacts = [];
  Iterable<String> get locationIds => locationMap.keys;
  Iterable<Location> get locations => locationMap.values;

  final noteIds = ValueNotifier<Iterable<String>>([]);
  final scheduleIds = ValueNotifier<Iterable<String>>([]);

  _ViewModel(this.userId) {
    addListenables([
      DataModel().userModel,
      DataModel().locationModel,
      DataModel().locationContactModel,
      DataModel().noteModel,
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    contact = await DataModel().userModel.getById(userId);
    if (contact == null) return;

    locationContacts =
        await DataModel().locationContactModel.getByUserIds([userId]);
    final locationIds = locationContacts.map((e) => e.locationId);
    final locations = await DataModel().locationModel.getByIds(locationIds);
    locationMap = {for (final location in locations) location.id: location};

    noteIds.value =
        (await DataModel().noteModel.getByUserId(userId)).map((e) => e.id);

    notifyListeners();
  }

  Future<void> addLocationId(String id) async {
    final locationContact = LocationContact.create()
      ..userId = userId
      ..locationId = id;
    await DataModel().locationContactModel.saveDirty([locationContact]);
    final location = await DataModel().locationModel.getById(id);
    locationMap[id] = location!;
    notifyListeners();
  }

  Future<void> removeLocationId(String id) async {
    final locationContact =
        locationContacts.firstWhere((e) => e.locationId == id);
    locationContact.isActive = false;
    await DataModel().locationContactModel.saveDirty(locationContacts);
    locationMap.remove(id);
    notifyListeners();
  }

  Future<void> setActive(User user, bool isActive) async {
    // Implement the logic to set the user's active status
    // Then refresh the contacts list
    await refresh();
  }
}

class DeactivateDialog extends StatelessWidget {
  final VoidCallback remove;

  const DeactivateDialog({required this.remove, Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) => AlertDialog(
        title: Text(AppLocalization.of(context).deactivate),
        content: Text(AppLocalization.of(context).activate),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(AppLocalization.of(context).cancel),
          ),
          ElevatedButton(
            onPressed: () {
              remove();
              Navigator.of(context).pop();
            },
            child: Text(AppLocalization.of(context).confirm),
          ),
        ],
      );
}
