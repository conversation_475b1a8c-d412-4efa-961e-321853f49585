import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../../../dataModel/data/location.dart';
import '../../../dataModel/data/user.dart';
import '../../../dataModel/data_model.dart';
import '../../../dialogs/web/expanded_employees_dialog.dart';
import '../../../dialogs/web/expanded_locations_dialog.dart';
import '../../../helpers/color_helper.dart';
import '../../../misc/app_localization.dart';
import '../../../misc/extensions.dart';
import '../../../widgets/calendar/calendar_widget.dart';
import '../../view_model_mixin.dart';
import '../../../state/permissions_state.dart';
import '../../../widgets/active_toggle.dart';
import '../../../widgets/decorated_text_field.dart';
import '../../../widgets/schedules_widget.dart';
import '../home/<USER>';
import '../my_scaffold.dart';
import 'widgets/calendar_body.dart';
import 'widgets/calendar_header_layout.dart';

class SchedulesPage extends StatefulWidget {
  final selectedDate;
  const SchedulesPage({super.key, this.selectedDate});

  @override
  State<SchedulesPage> createState() => _SchedulesPageState();
}

class _SchedulesPageState extends State<SchedulesPage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchAndRefreshCalendar();
    });
  }

  Future<void> _fetchAndRefreshCalendar() async {
    try {
      final calendarViewModel =
          Provider.of<CalendarViewModel>(context, listen: false);
      await calendarViewModel.fetchAndUpdateCalendar(context);
    } catch (e) {
      debugPrint('Error fetching and updating calendar: $e');
    }
  }

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider<_ViewModel>(
        create: (context) => _ViewModel(),
        child: MyScaffold(
          title: AppLocalizations.of(context)!.schedules,
          body: _Body(widget.selectedDate),
        ),
      );
}

class _BodyHeader extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 600;

    return IntrinsicHeight(
      child: Consumer<_ViewModel>(
        builder: (context, viewModel, child) {
          if (!viewModel.isRefreshed) return Container();
          final location = viewModel.location;
          final employee = viewModel.employee;

          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisAlignment: isMobile ? MainAxisAlignment.center : MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                      SizedBox(
                        width: isMobile? 160 : 300,
                        child: buildEmployee(context, viewModel, employee),
                      ),
                      const SizedBox(width: 10),
                      SizedBox(
                        width: isMobile? 160 : 300,
                        child: buildLocation(context, viewModel, location),
                      ),
                      if (!isMobile) const SizedBox(width: 10),
                      if (!isMobile)
                        Padding(
                        padding: const EdgeInsets.all(8),
                        child: ActiveToggle(viewModel.activeNotifier),
                        ),
                      ],
                    ),
                    if(!isMobile)
                    buildAddScheduleButton(context)
                  ],


                ),
                if (isMobile)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: ActiveToggle(viewModel.activeNotifier),
                  ),
                const SizedBox(height: 12),
                // if (isMobile) buildDetailsColumn(context, viewModel, employee, location)
                // else buildDetailsRow(context, viewModel, employee, location),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget buildEmployee(
      BuildContext context, _ViewModel viewModel, User? employee) => InkWell(
      onTap: () => unawaited(
        showDialog(
          context: context,
          builder: (context) => ExpandedEmployeesDialog(
            onSelection: (employeeId) => viewModel.setEmployee(employeeId),
          ),
        ),
      ),
      child: DecoratedContainer(
        padding: const EdgeInsets.all(8),
        labelText: AppLocalizations.of(context)!.employee,
        suffixIcon: InkWell(
          onTap: () => viewModel.clearEmployee(),
          child: const Icon(Icons.clear),
        ),
        child: Text(employee?.name ?? 'Employee' , style: TextStyle(fontWeight: FontWeight.bold , fontSize: 13, color: Colors.grey[500])),
      ),
    );

  Widget buildLocation(
      BuildContext context, _ViewModel viewModel, Location? location) => InkWell(
      onTap: () => unawaited(
        showDialog(
          context: context,
          builder: (context) => ExpandedLocationsDialog(
            onSelection: (locationId) => viewModel.setLocation(locationId),
          ),
        ),
      ),
      child: DecoratedContainer(
        padding: const EdgeInsets.all(8),
        labelText: AppLocalizations.of(context)!.location,
        suffixIcon: InkWell(
          onTap: () => viewModel.clearLocation(),
          child: const Icon(Icons.clear),
        ),
        child: Text(location?.name ?? 'Location', style:  TextStyle(fontWeight: FontWeight.bold , fontSize: 13, color: Colors.grey[500])),
      ),
    );

  Widget buildDetailsRow(BuildContext context, _ViewModel viewModel,
      User? employee, Location? location) => Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        buildDetailsButton(context, viewModel, employee, location),
        if (PermissionsState().editSchedules)
          buildAddScheduleButton(context),
      ],
    );

  Widget buildDetailsColumn(BuildContext context, _ViewModel viewModel,
      User? employee, Location? location) => Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        buildDetailsButton(context, viewModel, employee, location),
        if (PermissionsState().editSchedules) buildAddScheduleButton(context),
      ],
    );

  Widget buildDetailsButton(BuildContext context, _ViewModel viewModel,
      User? employee, Location? location) {
    final text = buildDetailsText(employee, location);

    return Padding(
      padding: const EdgeInsets.only(top: 8.0),
      child: OutlinedButton(
        style: OutlinedButton.styleFrom(
          side: BorderSide(
            color: ColorHelper.thePunchRed(),
          ),
        ),
        onPressed: () {},
        child: Text('Schedule Details for: $text'),
      ),
    );
  }

  String buildDetailsText(User? employee, Location? location) {
    if (employee != null && location != null) {
      return '${location.name} - ${employee.name}';
    } else if (employee != null) {
      return employee.name;
    } else if (location != null) {
      return location.name;
    }
    return '';
  }

  Widget buildAddScheduleButton(BuildContext context) => SizedBox(
      height: 40,
      child: ElevatedButton(
        onPressed: () => context.go('/schedules/addRepeatingSchedule'),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF4BA2E7),
          shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
          ),
          elevation: 0,
          padding: const EdgeInsets.symmetric(horizontal: 16),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
        Text(
          AppLocalization.of(context).addRepeatingSchedule,
          style: const TextStyle(color: Colors.white),
        ),
        const SizedBox(width: 8),
        Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
          ),
          padding: const EdgeInsets.all(4),
          child: const Icon(
            Icons.add,
            color: Color(0xFF4BA2E7),
            size: 16,
          ),
        ),
          ],
        ),
      ),
    );
}

class _Body extends StatelessWidget {
  final dynamic selectedDate;
  //final suffix = const Icon(Icons.calendar_month);
   _Body(this.selectedDate);
  final buttonStyle = OutlinedButton.styleFrom(
    side: BorderSide(
      color: ColorHelper.thePunchRed(),
    ),
  );
  @override
  Widget build(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 600;

    return Column(
        children: [

          Consumer<CalendarViewModel>(
        builder: (context, viewModel, child) {
          // Force week view on mobile
          if (isMobile && viewModel.calendarState != CalendarState.week) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              viewModel.setCalendarState(CalendarState.week);
            });
          }

          return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: isMobile ? MainAxisAlignment.center : MainAxisAlignment.start,
              children: [
                // Today button
                if(!isMobile)
                Container(
                  height: 36,
                  margin: const EdgeInsets.only(right: 16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(18),
                    border: Border.all(color: ColorHelper.thePunchAdminButtonBlue()),
                  ),
                  child: TextButton(
                    onPressed: () => viewModel.setDate(DateTime.now().dateOnly),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(18),
                      ),
                    ),
                    child: const Text(
                      'Today',
                      style: TextStyle(
                        color: Color(0xFF4BA2E7),
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),

                // Previous button
                Container(
                  width: 36,
                  height: 36,
                  margin: const EdgeInsets.only(right: 8),
                    decoration: BoxDecoration(
                    color: const Color.fromARGB(255, 185, 224, 253),
                    shape: BoxShape.circle,
                    border: Border.all(color: const Color(0xFFE6F4FF)),
                    ),
                  child: IconButton(
                    icon: const Icon(Icons.chevron_left, size: 20, color: Colors.white),
                    onPressed: () async {
                      if (isMobile) {
                        // On mobile, always navigate by week
                        await viewModel.goToPreviousWeek(context);
                      } else {
                        // On desktop, navigate based on current view
                        if (viewModel.calendarState == CalendarState.month) {
                          await viewModel.goToPreviousMonth(context);
                        } else if (viewModel.calendarState == CalendarState.day) {
                          viewModel.setDate(viewModel.date.addDays(-1));
                        } else {
                          await viewModel.goToPreviousWeek(context);
                        }
                      }
                    },
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ),

                // Next button
                Container(
                  width: 36,
                  height: 36,
                  margin: const EdgeInsets.only(right: 16),
                  decoration: BoxDecoration(
                    color: const Color.fromARGB(255, 185, 224, 253),
                    shape: BoxShape.circle,
                    border: Border.all(color: const Color(0xFFE6F4FF)),
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.chevron_right, size: 20, color: Colors.white),
                    onPressed: () async {
                      if (isMobile) {
                        // On mobile, always navigate by week
                        await viewModel.goToNextWeek(context);
                      } else {
                        // On desktop, navigate based on current view
                        if (viewModel.calendarState == CalendarState.month) {
                          await viewModel.goToNextMonth(context);
                        } else if (viewModel.calendarState == CalendarState.day) {
                          viewModel.setDate(viewModel.date.addDays(1));
                        } else {
                          await viewModel.goToNextWeek(context);
                        }
                      }
                    },
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ),

                // Date display
                Text(
                  '  ${isMobile || viewModel.calendarState == CalendarState.week ?
                    '${DateFormat(DateFormat.ABBR_MONTH_WEEKDAY_DAY).format(viewModel.date.startOfWeek)} - ${DateFormat(DateFormat.ABBR_MONTH_WEEKDAY_DAY).format(viewModel.date.startOfWeek.add(const Duration(days: 6)))}' :
                    viewModel.calendarState == CalendarState.day ?
                    '${DateFormat(DateFormat.ABBR_MONTH_WEEKDAY_DAY).format(DateTime(viewModel.date.year, viewModel.date.month, viewModel.date.day))} - ${DateFormat(DateFormat.ABBR_MONTH_WEEKDAY_DAY).format(DateTime(viewModel.date.year, viewModel.date.month, viewModel.date.day+2))}' :
                    DateFormat(DateFormat.YEAR_MONTH).format(DateTime(viewModel.date.year, viewModel.date.month))} ',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),

            // Hide calendar state dropdown on mobile
            if (!isMobile)
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                padding: const EdgeInsets.symmetric(horizontal: 12),
                decoration: BoxDecoration(
                color: ColorHelper.thePunchDarkBlue(),
                borderRadius: BorderRadius.circular(8),
                ),
                child: DropdownButtonHideUnderline(
                child: DropdownButton<CalendarState>(
                  value: viewModel.calendarState,
                  dropdownColor: ColorHelper.thePunchDarkBlue(),
                  style: const TextStyle(color: Colors.white),
                  icon: const Icon(Icons.arrow_drop_down, color: Colors.white),
                  items: const [
                  DropdownMenuItem(
                    value: CalendarState.month,
                    child: Text('Month'),
                  ),
                  DropdownMenuItem(
                    value: CalendarState.week,
                    child: Text('Week'),
                  ),
                  DropdownMenuItem(
                    value: CalendarState.day,
                    child: Text('Day'),
                  ),
                  ],
                  onChanged: (value) {
                  if (value != null) viewModel.setCalendarState(value);
                  },
                ),
                ),
              ),
            ],
        );
        },
      ),
          _BodyHeader(),
            Consumer<CalendarViewModel>(
            builder: (context, viewModel, _) {
              final isMobile = MediaQuery.of(context).size.width < 600;
              return ChangeNotifierProvider.value(
                value: viewModel,
                child: isMobile
                  ? CalendarWeekHeaderLayout()  // Always show week header on mobile
                  : viewModel.calendarState==CalendarState.day? CalendarDayHeaderLayout(
                      date: viewModel.date,
                    ):viewModel.calendarState==CalendarState.week?CalendarWeekHeaderLayout():Container(),
              );
            })


            ,



          Flexible(
            child: Consumer<_ViewModel>(
              builder: (context, viewModel, child) => SchedulesWidget(
                selectedDate: selectedDate,
                employeeIds: viewModel.employeeId == null
                    ? null
                    : [viewModel.employeeId!],
                locationIds: viewModel.locationId == null
                    ? null
                    : [viewModel.locationId!],
                active: viewModel.activeNotifier.value,
                scrollable: true,
                showEmployeeNames: viewModel.showEmployeeNames.value,
              ),
            ),
          ),
        ],
      );
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  final scheduleIds = ValueNotifier<Iterable<String>>([]);
  var activeNotifier = ValueNotifier(ActiveToggleState.active);
  final showEmployeeNames = ValueNotifier<bool>(true);
  String? employeeId;
  User? employee;
  String? locationId;
  Location? location;

  _ViewModel() {
    addListenables([
      DataModel().scheduleModel,
      DataModel().userModel,
      DataModel().locationModel,
      activeNotifier,
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    scheduleIds.value = [];
    employee = null;
    location = null;

    var schedules = await DataModel().scheduleModel.all;
    switch (activeNotifier.value) {
      case ActiveToggleState.active:
        schedules = schedules.where((e) => e.isActive);
        break;
      case ActiveToggleState.inactive:
        schedules = schedules.where((e) => !e.isActive);
        break;
      case ActiveToggleState.all:
      case ActiveToggleState.both:
        break;
    }

    if (employeeId != null) {
      employee = await DataModel().userModel.getById(employeeId!);
      schedules = schedules.where((e) => e.userId == employeeId!);
    }
    if (locationId != null) {
      location = await DataModel().locationModel.getById(locationId!);
      schedules = schedules.where((e) => e.locationId == locationId!);
    }

    scheduleIds.value = schedules.map((e) => e.id);
    showEmployeeNames.value = locationId != null && employeeId == null;

    notifyListeners();
  }

  Future<void> setLocation(String locationId) async {
    if (this.locationId == locationId) return;
    this.locationId = locationId;
    await refresh();
  }

  void clearLocation() {
    if (locationId == null) return;
    locationId = null;
    unawaited(refresh());
  }

  Future<void> setEmployee(String employeeId) async {
    if (this.employeeId == employeeId) return;
    this.employeeId = employeeId;
    await refresh();
  }

  void clearEmployee() {
    if (employeeId == null) return;
    employeeId = null;
    unawaited(refresh());
  }
}
